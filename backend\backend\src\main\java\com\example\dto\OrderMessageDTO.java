package com.example.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 訂單消息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 訂單號
     */
    private String orderNumber;

    /**
     * 用戶ID
     */
    private Long userId;

    /**
     * 用戶郵箱
     */
    private String userEmail;

    /**
     * 訂單金額
     */
    private Double orderAmount;

    /**
     * 創建時間
     */
    private LocalDateTime createTime;

    /**
     * 操作類型
     */
    private OperationType operationType;

    public enum OperationType {
        CANCEL_ORDER,      // 取消訂單
        PAYMENT_TIMEOUT,   // 支付超時
        INVENTORY_RESTORE  // 庫存恢復
    }

    public OrderMessageDTO(String orderNumber, Long userId, OperationType operationType) {
        this.orderNumber = orderNumber;
        this.userId = userId;
        this.operationType = operationType;
        this.createTime = LocalDateTime.now();
    }

    public OrderMessageDTO(String orderNumber, Long userId, String userEmail, Double orderAmount, OperationType operationType) {
        this.orderNumber = orderNumber;
        this.userId = userId;
        this.userEmail = userEmail;
        this.orderAmount = orderAmount;
        this.operationType = operationType;
        this.createTime = LocalDateTime.now();
    }
}