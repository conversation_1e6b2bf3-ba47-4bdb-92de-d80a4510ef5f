{"mcpServers": {"g-search": {"isActive": true, "command": "npx", "args": ["-y", "g-search-mcp"], "name": "g-search"}, "fetcher-mcp": {"isActive": true, "name": "fetcher-mcp", "type": "stdio", "registryUrl": "", "command": "npx", "args": ["-y", "fetcher-mcp@latest"]}, "sequential-thinking": {"isActive": true, "name": "sequential-thinking", "type": "stdio", "registryUrl": "", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "brave-search": {"isActive": true, "command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAv-vY0F1SB5HFaqrMUR7ALYBS3oaF"}, "name": "brave-search"}, "fetch": {"isActive": true, "command": "uvx", "args": ["mcp-server-fetch"], "name": "fetch"}, "filesystem": {"isActive": true, "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:\\", "D:\\"], "name": "filesystem"}, "dbhub-mysql-npx": {"isActive": true, "name": "dbhub-mysql-npx", "type": "stdio", "registryUrl": "", "command": "npx", "args": ["-y", "@bytebase/dbhub", "--transport", "stdio", "--dsn", "mysql://root:NDI1694T5q0WDy3p11bhtz4lo489W29D3Xd@localhost:3306/blog_250704"]}, "21st-dev": {"isActive": true, "command": "npx", "args": ["-y", "@21st-dev/magic@latest", "API_KEY=\"48b60973988bd36a5cb5d621623c1bb6f85adca0079fb46711f650062d0afa4f\""], "name": "21st-dev"}, "everart-forge": {"isActive": true, "command": "node", "args": ["D:/Delta/tools/everart-forge-mcp/everart-forge-mcp-main/build/index.js"], "env": {"EVERART_API_KEY": "everart-d8e3xSrjrHD6XgBmc_e0qHGcFWdhHkmxDNegmEsmw2o"}, "disabled": false, "autoApprove": [], "name": "everart-forge"}, "browserbase": {"isActive": true, "name": "browserbase", "command": "node", "args": ["D:/Delta/tools/mcp-server-browserbase/mcp-server-browserbase-main/browserbase/dist/index.js"], "env": {"BROWSERBASE_API_KEY": "bb_live_J-7ritMzY7fTJibojhvQIP_ROd8", "BROWSERBASE_PROJECT_ID": "16d22750-7d7d-436d-813d-3e8a2a8eed45"}}, "image-downloader": {"isActive": true, "name": "image-downloader", "command": "node", "args": ["D:\\Delta\\tools\\mcp-image-downloader\\mcp-image-downloader-main\\build\\index.js"]}, "firecrawl-mcp": {"isActive": true, "command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-38cbe4f17aee4a979de06587072d1f3f"}, "name": "firecrawl-mcp"}, "quickchart-server": {"isActive": true, "command": "npx", "args": ["-y", "@gongrzhe/quickchart-mcp-server"], "name": "quickchart-server"}, "markdown2pdf": {"isActive": true, "command": "node", "args": ["D:/Delta/tools/markdown2pdf-mcp/markdown2pdf-mcp-main/build/index.js"], "env": {"M2P_OUTPUT_DIR": "D:/Delta/tools/markdown2pdf-mcp/markdown2pdf-mcp-main/output"}, "name": "markdown2pdf"}, "replicate-flux-mcp": {"isActive": true, "command": "npx", "args": ["-y", "replicate-flux-mcp"], "env": {"REPLICATE_API_TOKEN": "****************************************"}, "name": "replicate-flux-mcp"}, "playwright": {"isActive": true, "name": "playwright", "type": "stdio", "registryUrl": "", "command": "npx", "args": ["@playwright/mcp@0.0.15"]}, "interpreter": {"isActive": true, "command": "uvx", "args": ["python-interpreter-mcp"], "name": "interpreter"}, "Context7": {"isActive": true, "type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@1.0.5"], "name": "Context7"}, "mcp-deepwiki": {"isActive": true, "command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@0.0.8"], "name": "mcp-<PERSON><PERSON><PERSON>"}, "repomix": {"command": "npx", "args": ["-y", "repomix", "--mcp"]}, "browser-tools-mcp": {"isActive": true, "command": "npx", "args": ["-y", "@agentdeskai/browser-tools-mcp@1.2.0"]}, "mcp-server-time": {"command": "uvx", "args": ["mcp-server-time", "--local-timezone=Asia/Shanghai"]}, "duolabmeng6-interactive-feedback-mcp": {"command": "uvx", "args": ["interactive-feedback-mcp"], "timeout": 600, "autoApprove": ["interactive_feedback"]}, "code-reasoning": {"command": "npx", "args": ["-y", "@mettamatt/code-reasoning"]}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "autoApprove": ["interactive_feedback"]}, "mcp-server-chart": {"command": "npx", "args": ["-y", "@antv/mcp-server-chart"]}, "interactive-feedback": {"command": "uvx", "args": ["interactive-feedback@latest"], "timeout": 600, "autoApprove": ["interactive_feedback"]}, "excel-auto-cpp": {"url": "http://localhost:8888/sse"}, "redis": {"command": "npx", "args": ["@gongrzhe/server-redis-mcp@1.0.0", "redis://localhost:6379"]}, "Redis MCP Server": {"command": "uvx", "args": ["--from", "git+https://github.com/redis/mcp-redis.git", "redis-mcp-server", "--url", "redis://localhost:6379/0"]}, "memory-bank": {"command": "npx", "args": ["@neko0721/memory-bank-mcp"], "timeout": 600}, "mindmap": {"isActive": true, "command": "uvx", "args": ["mindmap-mcp-server", "--return-type", "filePath"], "name": "mindmap"}}}