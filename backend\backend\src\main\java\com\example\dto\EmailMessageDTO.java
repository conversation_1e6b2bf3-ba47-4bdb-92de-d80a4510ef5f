package com.example.dto;

import com.example.service.EmailService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 郵件消息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmailMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 收件人郵箱
     */
    private String email;

    /**
     * 郵件類型
     */
    private EmailService.Type type;

    /**
     * 驗證碼（可選，如果不提供則自動生成）
     */
    private String verificationCode;

    /**
     * 消息創建時間
     */
    private LocalDateTime createTime;

    /**
     * 重試次數
     */
    private Integer retryCount = 0;

    /**
     * 最大重試次數
     */
    private Integer maxRetryCount = 3;

    /**
     * 用戶ID（可選，用於關聯用戶）
     */
    private Long userId;

    /**
     * 是否需要檢查頻率限制
     */
    private Boolean checkRateLimit = true;

    public EmailMessageDTO(String email, EmailService.Type type) {
        this.email = email;
        this.type = type;
        this.createTime = LocalDateTime.now();
    }

    public EmailMessageDTO(String email, EmailService.Type type, Long userId) {
        this.email = email;
        this.type = type;
        this.userId = userId;
        this.createTime = LocalDateTime.now();
    }

    /**
     * 增加重試次數
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }

    /**
     * 是否超過最大重試次數
     */
    public boolean isMaxRetryExceeded() {
        return this.retryCount >= this.maxRetryCount;
    }
}