package com.example.service;

import com.example.config.RabbitMQConfig;
import com.example.dto.OrderMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 訂單生產者服務
 * 處理訂單相關的延時任務
 */
@Service
@Slf4j
public class OrderProducerService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 發送延時取消訂單消息
     * 訂單創建後，如果30分鐘內未支付，將自動取消
     */
    public void sendDelayedOrderCancellation(String orderNumber, Long userId) {
        OrderMessageDTO message = new OrderMessageDTO(orderNumber, userId, OrderMessageDTO.OperationType.CANCEL_ORDER);
        
        rabbitTemplate.convertAndSend(
                RabbitMQConfig.ORDER_EXCHANGE, 
                RabbitMQConfig.ORDER_DELAY_ROUTING_KEY, 
                message
        );
        
        log.info("延時取消訂單消息已發送: orderNumber={}, userId={}", orderNumber, userId);
    }

    /**
     * 發送延時取消訂單消息（包含完整信息）
     */
    public void sendDelayedOrderCancellation(String orderNumber, Long userId, String userEmail, Double orderAmount) {
        OrderMessageDTO message = new OrderMessageDTO(
                orderNumber, 
                userId, 
                userEmail, 
                orderAmount, 
                OrderMessageDTO.OperationType.CANCEL_ORDER
        );
        
        rabbitTemplate.convertAndSend(
                RabbitMQConfig.ORDER_EXCHANGE, 
                RabbitMQConfig.ORDER_DELAY_ROUTING_KEY, 
                message
        );
        
        log.info("延時取消訂單消息已發送: orderNumber={}, userId={}, email={}, amount={}", 
                orderNumber, userId, userEmail, orderAmount);
    }

    /**
     * 發送支付超時消息
     */
    public void sendPaymentTimeoutMessage(String orderNumber, Long userId) {
        OrderMessageDTO message = new OrderMessageDTO(orderNumber, userId, OrderMessageDTO.OperationType.PAYMENT_TIMEOUT);
        
        rabbitTemplate.convertAndSend(
                RabbitMQConfig.ORDER_EXCHANGE, 
                RabbitMQConfig.ORDER_DELAY_ROUTING_KEY, 
                message
        );
        
        log.info("支付超時消息已發送: orderNumber={}, userId={}", orderNumber, userId);
    }

    /**
     * 發送庫存恢復消息
     */
    public void sendInventoryRestoreMessage(String orderNumber, Long userId) {
        OrderMessageDTO message = new OrderMessageDTO(orderNumber, userId, OrderMessageDTO.OperationType.INVENTORY_RESTORE);
        
        rabbitTemplate.convertAndSend(
                RabbitMQConfig.ORDER_EXCHANGE, 
                RabbitMQConfig.ORDER_DELAY_ROUTING_KEY, 
                message
        );
        
        log.info("庫存恢復消息已發送: orderNumber={}, userId={}", orderNumber, userId);
    }

    /**
     * 立即發送訂單處理消息（不延時）
     * 用於需要立即處理的場景
     */
    public void sendImmediateOrderProcess(OrderMessageDTO orderMessage) {
        rabbitTemplate.convertAndSend(
                RabbitMQConfig.DEAD_LETTER_EXCHANGE, 
                RabbitMQConfig.ORDER_PROCESS_ROUTING_KEY, 
                orderMessage
        );
        
        log.info("立即訂單處理消息已發送: orderNumber={}, operationType={}", 
                orderMessage.getOrderNumber(), orderMessage.getOperationType());
    }

    /**
     * 批量發送延時取消訂單消息
     */
    public void sendBatchDelayedOrderCancellation(String[] orderNumbers, Long[] userIds) {
        if (orderNumbers.length != userIds.length) {
            throw new IllegalArgumentException("訂單號數組和用戶ID數組長度不匹配");
        }
        
        for (int i = 0; i < orderNumbers.length; i++) {
            sendDelayedOrderCancellation(orderNumbers[i], userIds[i]);
        }
        
        log.info("批量延時取消訂單消息已發送: count={}", orderNumbers.length);
    }

    /**
     * 取消延時任務（如果訂單已支付，需要取消延時取消任務）
     * 注意：RabbitMQ本身不支持直接取消已發送的消息，
     * 這裡的實現是在消費者端檢查訂單狀態來決定是否執行取消操作
     */
    public void cancelDelayedOrderCancellation(String orderNumber) {
        // 在實際應用中，可以考慮以下方案：
        // 1. 使用Redis記錄已支付訂單，消費者檢查Redis狀態
        // 2. 使用RabbitMQ的DLX和TTL組合實現可撤銷的延時任務
        // 3. 使用專門的延時任務調度器如Quartz
        
        log.info("請求取消延時任務: orderNumber={} (將在消費者端檢查訂單狀態)", orderNumber);
    }
}