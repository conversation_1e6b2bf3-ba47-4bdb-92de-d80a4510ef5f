package com.example.controller;

import com.example.dto.ApiResponse;
import com.example.service.EmailProducerService;
import com.example.service.EmailService;
import com.example.service.OrderProducerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * RabbitMQ測試控制器
 * 用於測試消息隊列功能
 */
@RestController
@RequestMapping("/api/test/rabbitmq")
@Tag(name = "RabbitMQ測試", description = "RabbitMQ消息隊列功能測試接口")
@Slf4j
public class RabbitMQTestController {

    @Autowired
    private EmailProducerService emailProducerService;

    @Autowired
    private OrderProducerService orderProducerService;

    /**
     * 測試異步發送註冊郵件
     */
    @PostMapping("/email/registration")
    @Operation(summary = "測試發送註冊驗證碼郵件", description = "通過RabbitMQ異步發送註冊驗證碼郵件")
    public ApiResponse<String> testRegistrationEmail(
            @Parameter(description = "郵箱地址", required = true)
            @RequestParam String email) {
        try {
            emailProducerService.sendRegistrationEmail(email);
            return ApiResponse.success("註冊郵件已提交到隊列，請檢查郵箱");
        } catch (Exception e) {
            log.error("測試註冊郵件失敗: email={}", email, e);
            return ApiResponse.error("發送失敗: " + e.getMessage());
        }
    }

    /**
     * 測試異步發送登入郵件
     */
    @PostMapping("/email/login")
    @Operation(summary = "測試發送登入驗證碼郵件", description = "通過RabbitMQ異步發送登入驗證碼郵件")
    public ApiResponse<String> testLoginEmail(
            @Parameter(description = "郵箱地址", required = true)
            @RequestParam String email) {
        try {
            emailProducerService.sendLoginEmail(email);
            return ApiResponse.success("登入郵件已提交到隊列，請檢查郵箱");
        } catch (Exception e) {
            log.error("測試登入郵件失敗: email={}", email, e);
            return ApiResponse.error("發送失敗: " + e.getMessage());
        }
    }

    /**
     * 測試異步發送密碼重置郵件
     */
    @PostMapping("/email/password-reset")
    @Operation(summary = "測試發送密碼重置驗證碼郵件", description = "通過RabbitMQ異步發送密碼重置驗證碼郵件")
    public ApiResponse<String> testPasswordResetEmail(
            @Parameter(description = "郵箱地址", required = true)
            @RequestParam String email) {
        try {
            emailProducerService.sendPasswordResetEmail(email);
            return ApiResponse.success("密碼重置郵件已提交到隊列，請檢查郵箱");
        } catch (Exception e) {
            log.error("測試密碼重置郵件失敗: email={}", email, e);
            return ApiResponse.error("發送失敗: " + e.getMessage());
        }
    }

    /**
     * 測試發送延時取消訂單消息
     */
    @PostMapping("/order/delay-cancel")
    @Operation(summary = "測試發送延時取消訂單消息", description = "測試30分鐘後自動取消訂單的功能")
    public ApiResponse<String> testDelayedOrderCancellation(
            @Parameter(description = "訂單號", required = true)
            @RequestParam String orderNumber,
            @Parameter(description = "用戶ID", required = true)
            @RequestParam Long userId) {
        try {
            orderProducerService.sendDelayedOrderCancellation(orderNumber, userId);
            return ApiResponse.success("延時取消訂單消息已發送，將在30分鐘後處理");
        } catch (Exception e) {
            log.error("測試延時取消訂單失敗: orderNumber={}, userId={}", orderNumber, userId, e);
            return ApiResponse.error("發送失敗: " + e.getMessage());
        }
    }

    /**
     * 測試發送支付超時消息
     */
    @PostMapping("/order/payment-timeout")
    @Operation(summary = "測試發送支付超時消息", description = "測試支付超時處理功能")
    public ApiResponse<String> testPaymentTimeout(
            @Parameter(description = "訂單號", required = true)
            @RequestParam String orderNumber,
            @Parameter(description = "用戶ID", required = true)
            @RequestParam Long userId) {
        try {
            orderProducerService.sendPaymentTimeoutMessage(orderNumber, userId);
            return ApiResponse.success("支付超時消息已發送，將在30分鐘後處理");
        } catch (Exception e) {
            log.error("測試支付超時失敗: orderNumber={}, userId={}", orderNumber, userId, e);
            return ApiResponse.error("發送失敗: " + e.getMessage());
        }
    }

    /**
     * 測試批量發送郵件
     */
    @PostMapping("/email/batch")
    @Operation(summary = "測試批量發送郵件", description = "批量發送不同類型的驗證碼郵件")
    public ApiResponse<String> testBatchEmail(
            @Parameter(description = "郵箱地址列表（逗號分隔）", required = true)
            @RequestParam String emails,
            @Parameter(description = "郵件類型", required = true)
            @RequestParam String type) {
        try {
            String[] emailArray = emails.split(",");
            int successCount = 0;
            int failCount = 0;

            for (String email : emailArray) {
                email = email.trim();
                if (email.isEmpty()) continue;

                try {
                    switch (type.toLowerCase()) {
                        case "registration":
                            emailProducerService.sendRegistrationEmail(email);
                            break;
                        case "login":
                            emailProducerService.sendLoginEmail(email);
                            break;
                        case "password-reset":
                            emailProducerService.sendPasswordResetEmail(email);
                            break;
                        default:
                            emailProducerService.sendRegistrationEmail(email);
                    }
                    successCount++;
                } catch (Exception e) {
                    log.error("批量發送郵件失敗: email={}, type={}", email, type, e);
                    failCount++;
                }
            }

            return ApiResponse.success(String.format("批量郵件發送完成: 成功%d個，失敗%d個", successCount, failCount));
        } catch (Exception e) {
            log.error("批量發送郵件失敗: emails={}, type={}", emails, type, e);
            return ApiResponse.error("批量發送失敗: " + e.getMessage());
        }
    }

    /**
     * 測試消息隊列狀態
     */
    @GetMapping("/status")
    @Operation(summary = "檢查RabbitMQ連接狀態", description = "檢查消息隊列服務是否正常運行")
    public ApiResponse<String> checkRabbitMQStatus() {
        try {
            // 發送一個測試消息來檢查連接
            emailProducerService.sendEmailWithoutRateLimit("<EMAIL>", EmailService.Type.REGISTRATION);
            return ApiResponse.success("RabbitMQ連接正常，測試消息已發送");
        } catch (Exception e) {
            log.error("RabbitMQ連接測試失敗", e);
            return ApiResponse.error("RabbitMQ連接異常: " + e.getMessage());
        }
    }

    /**
     * 測試隊列性能
     */
    @PostMapping("/performance")
    @Operation(summary = "測試隊列性能", description = "發送大量消息測試隊列處理性能")
    public ApiResponse<String> testQueuePerformance(
            @Parameter(description = "消息數量", required = true)
            @RequestParam(defaultValue = "100") int messageCount,
            @Parameter(description = "郵件類型", required = true)
            @RequestParam(defaultValue = "registration") String type) {
        try {
            long startTime = System.currentTimeMillis();

            for (int i = 0; i < messageCount; i++) {
                String testEmail = "test" + i + "@example.com";
                switch (type.toLowerCase()) {
                    case "registration":
                        emailProducerService.sendEmailWithoutRateLimit(testEmail, EmailService.Type.REGISTRATION);
                        break;
                    case "login":
                        emailProducerService.sendEmailWithoutRateLimit(testEmail, EmailService.Type.LOGIN);
                        break;
                    case "password-reset":
                        emailProducerService.sendEmailWithoutRateLimit(testEmail, EmailService.Type.PASSWORD_RESET);
                        break;
                }
            }

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            String result = String.format("性能測試完成: 發送%d條消息，耗時%dms，平均%.2fms/條", 
                    messageCount, duration, (double) duration / messageCount);

            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("隊列性能測試失敗: messageCount={}, type={}", messageCount, type, e);
            return ApiResponse.error("性能測試失敗: " + e.getMessage());
        }
    }
}