package com.example.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 購物車項目實體類
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Entity
@Table(name = "cart_items")
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "cart", "product"})
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CartItem {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 購物車ID
     */
    @Column(name = "cart_id", nullable = false)
    private Long cartId;
    
    /**
     * 商品ID
     */
    @Column(name = "product_id", nullable = false)
    private Long productId;
    
    /**
     * 商品數量
     */
    @Column(name = "quantity", nullable = false)
    private Integer quantity;
    
    /**
     * 商品價格快照（加入購物車時的價格）
     */
    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    private BigDecimal price;
    
    /**
     * 商品名稱快照
     */
    @Column(name = "product_name", length = 200)
    private String productName;
    
    /**
     * 商品圖片快照
     */
    @Column(name = "product_image_url")
    private String productImageUrl;
    
    /**
     * 是否選中（用於結算）：1-選中，0-未選中
     */
    @Column(name = "selected", nullable = false)
    private Integer selected = 1;
    
    /**
     * 創建時間
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新時間
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // 關聯關係 - 購物車
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cart_id", insertable = false, updatable = false)
    private Cart cart;
    
    // 關聯關係 - 商品
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", insertable = false, updatable = false)
    private Product product;

    /**
     * 獲取商品庫存（用於前端顯示）
     */
    @JsonProperty("stock")
    public Integer getStock() {
        return product != null ? product.getStock() : 0;
    }
    
    /**
     * 選中狀態枚舉
     */
    public static class Selected {
        public static final int NOT_SELECTED = 0;  // 未選中
        public static final int SELECTED = 1;      // 選中
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 構造函數 - 創建新購物車項目
     */
    public CartItem(Long cartId, Long productId, Integer quantity, BigDecimal price, String productName, String productImageUrl) {
        this.cartId = cartId;
        this.productId = productId;
        this.quantity = quantity;
        this.price = price;
        this.productName = productName;
        this.productImageUrl = productImageUrl;
        this.selected = Selected.SELECTED;
    }
    
    /**
     * 計算小計
     */
    public BigDecimal getSubtotal() {
        return price.multiply(BigDecimal.valueOf(quantity));
    }
}
