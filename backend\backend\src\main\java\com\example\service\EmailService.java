package com.example.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.Duration;

@Service
@Slf4j
public class EmailService {

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private RedisService redisService;

    @Autowired
    private EmailProducerService emailProducerService;

    private static final String CHARACTERS = "0123456789";
    private static final int CODE_LENGTH = 6;
    private final SecureRandom random = new SecureRandom();

    public enum Type {
        REGISTRATION,  // 註冊驗證
        LOGIN,         // 登入驗證
        PASSWORD_RESET // 密碼重置
    }

    /**
     * 發送驗證碼郵件（異步方式）
     * 使用RabbitMQ隊列異步處理郵件發送，提高響應速度
     */
    public boolean sendVerificationCode(String email, Type type) {
        try {
            // 提前檢查頻率限制，避免無效消息進入隊列
            if (!redisService.checkEmailRateLimit5Min(email)) {
                log.warn("郵件發送頻率超限（5分鐘）: {}", email);
                throw new RuntimeException("5分鐘內最多發送2次驗證碼");
            }
            
            if (!redisService.checkEmailRateLimitDay(email)) {
                log.warn("郵件發送頻率超限（一天）: {}", email);
                throw new RuntimeException("一天內最多發送5次驗證碼");
            }
            
            // 將郵件發送任務添加到隊列中異步處理
            switch (type) {
                case REGISTRATION:
                    emailProducerService.sendRegistrationEmail(email);
                    break;
                case LOGIN:
                    emailProducerService.sendLoginEmail(email);
                    break;
                case PASSWORD_RESET:
                    emailProducerService.sendPasswordResetEmail(email);
                    break;
                default:
                    emailProducerService.sendRegistrationEmail(email);
            }
            
            log.info("驗證碼郵件任務已提交到隊列: email={}, type={}", email, type);
            return true;
            
        } catch (Exception e) {
            log.error("提交郵件發送任務失敗: {}", email, e);
            throw new RuntimeException("發送驗證碼失敗: " + e.getMessage());
        }
    }

    /**
     * 同步發送驗證碼郵件（保留原有方法作為備用）
     */
    public boolean sendVerificationCodeSync(String email, Type type) {
        try {
            // 檢查頻率限制
            if (!redisService.checkEmailRateLimit5Min(email)) {
                log.warn("郵件發送頻率超限（5分鐘）: {}", email);
                throw new RuntimeException("5分鐘內最多發送2次驗證碼");
            }
            
            if (!redisService.checkEmailRateLimitDay(email)) {
                log.warn("郵件發送頻率超限（一天）: {}", email);
                throw new RuntimeException("一天內最多發送5次驗證碼");
            }
            
            // 生成驗證碼
            String verificationCode = generateVerificationCode();

            // 保存到 Redis（5分鐘過期）
            String redisKey = "email_verification:" + email;
            redisService.set(redisKey, verificationCode, Duration.ofMinutes(5)); // 5分鐘過期

            // 發送郵件
            sendEmail(email, verificationCode, type);
            
            log.info("驗證碼郵件發送成功: {}", email);
            return true;
            
        } catch (Exception e) {
            log.error("發送驗證碼郵件失敗: {}", email, e);
            throw new RuntimeException("發送驗證碼失敗: " + e.getMessage());
        }
    }
    
    /**
     * 驗證驗證碼
     */
    public boolean verifyCode(String email, String code) {
        String redisKey = "email_verification:" + email;
        Object storedCodeObj = redisService.get(redisKey);

        if (storedCodeObj == null) {
            log.warn("驗證碼不存在或已過期: email={}, code={}", email, code);
            return false;
        }

        String storedCode = storedCodeObj.toString();

        if (!storedCode.equals(code)) {
            log.warn("驗證碼不匹配: email={}, code={}", email, code);
            return false;
        }

        // 驗證成功，刪除 Redis 中的驗證碼
        redisService.delete(redisKey);

        log.info("驗證碼驗證成功: email={}", email);
        return true;
    }
    
    /**
     * 生成6位數字驗證碼
     */
    private String generateVerificationCode() {
        StringBuilder code = new StringBuilder(CODE_LENGTH);
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }
        return code.toString();
    }
    
    /**
     * 發送郵件
     */
    private void sendEmail(String to, String verificationCode, Type type) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(to);

        String subject;
        String text;

        switch (type) {
            case REGISTRATION:
                subject = "註冊驗證碼";
                text = String.format("您的註冊驗證碼是：%s\n\n驗證碼5分鐘內有效，請及時使用。", verificationCode);
                break;
            case LOGIN:
                subject = "登入驗證碼";
                text = String.format("您的登入驗證碼是：%s\n\n驗證碼5分鐘內有效，請及時使用。", verificationCode);
                break;
            case PASSWORD_RESET:
                subject = "密碼重置驗證碼";
                text = String.format("您的密碼重置驗證碼是：%s\n\n驗證碼5分鐘內有效，請及時使用。", verificationCode);
                break;
            default:
                subject = "驗證碼";
                text = String.format("您的驗證碼是：%s\n\n驗證碼5分鐘內有效，請及時使用。", verificationCode);
        }
        
        message.setSubject(subject);
        message.setText(text);
        
        mailSender.send(message);
    }
    
    /**
     * 獲取郵件發送統計信息
     */
    public String getEmailSendStats(String email) {
        long count5Min = redisService.getEmailCount5Min(email);
        long countDay = redisService.getEmailCountDay(email);
        
        return String.format("5分鐘內已發送: %d/2次, 今日已發送: %d/5次", count5Min, countDay);
    }
}
