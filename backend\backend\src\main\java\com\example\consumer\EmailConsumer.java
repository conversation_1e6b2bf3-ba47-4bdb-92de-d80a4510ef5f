package com.example.consumer;

import com.example.config.RabbitMQConfig;
import com.example.dto.EmailMessageDTO;
import com.example.service.EmailService;
import com.example.service.RedisService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.security.SecureRandom;
import java.time.Duration;

/**
 * 郵件消費者
 * 處理各種類型的郵件發送任務
 */
@Component
@Slf4j
public class EmailConsumer {

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private RedisService redisService;

    private static final String CHARACTERS = "0123456789";
    private static final int CODE_LENGTH = 6;
    private final SecureRandom random = new SecureRandom();

    /**
     * 處理註冊郵件
     */
    @RabbitListener(queues = RabbitMQConfig.EMAIL_REGISTRATION_QUEUE)
    public void handleRegistrationEmail(EmailMessageDTO emailMessage, Message message, Channel channel) throws IOException {
        try {
            log.info("開始處理註冊郵件: {}", emailMessage.getEmail());
            processEmailMessage(emailMessage);
            
            // 手動確認消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.info("註冊郵件處理成功: {}", emailMessage.getEmail());
            
        } catch (Exception e) {
            log.error("處理註冊郵件失敗: {}", emailMessage.getEmail(), e);
            handleEmailError(emailMessage, message, channel, e);
        }
    }

    /**
     * 處理登入郵件
     */
    @RabbitListener(queues = RabbitMQConfig.EMAIL_LOGIN_QUEUE)
    public void handleLoginEmail(EmailMessageDTO emailMessage, Message message, Channel channel) throws IOException {
        try {
            log.info("開始處理登入郵件: {}", emailMessage.getEmail());
            processEmailMessage(emailMessage);
            
            // 手動確認消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.info("登入郵件處理成功: {}", emailMessage.getEmail());
            
        } catch (Exception e) {
            log.error("處理登入郵件失敗: {}", emailMessage.getEmail(), e);
            handleEmailError(emailMessage, message, channel, e);
        }
    }

    /**
     * 處理密碼重置郵件
     */
    @RabbitListener(queues = RabbitMQConfig.EMAIL_PASSWORD_RESET_QUEUE)
    public void handlePasswordResetEmail(EmailMessageDTO emailMessage, Message message, Channel channel) throws IOException {
        try {
            log.info("開始處理密碼重置郵件: {}", emailMessage.getEmail());
            processEmailMessage(emailMessage);
            
            // 手動確認消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.info("密碼重置郵件處理成功: {}", emailMessage.getEmail());
            
        } catch (Exception e) {
            log.error("處理密碼重置郵件失敗: {}", emailMessage.getEmail(), e);
            handleEmailError(emailMessage, message, channel, e);
        }
    }

    /**
     * 處理郵件消息的核心邏輯
     */
    private void processEmailMessage(EmailMessageDTO emailMessage) {
        String email = emailMessage.getEmail();
        EmailService.Type type = emailMessage.getType();

        // 檢查頻率限制（如果需要）
        if (emailMessage.getCheckRateLimit()) {
            if (!redisService.checkEmailRateLimit5Min(email)) {
                throw new RuntimeException("5分鐘內最多發送2次驗證碼");
            }
            
            if (!redisService.checkEmailRateLimitDay(email)) {
                throw new RuntimeException("一天內最多發送5次驗證碼");
            }
        }

        // 生成或使用提供的驗證碼
        String verificationCode = emailMessage.getVerificationCode();
        if (verificationCode == null || verificationCode.isEmpty()) {
            verificationCode = generateVerificationCode();
        }

        // 保存驗證碼到Redis（5分鐘過期）
        String redisKey = "email_verification:" + email;
        redisService.set(redisKey, verificationCode, Duration.ofMinutes(5));

        // 發送郵件
        sendEmail(email, verificationCode, type);
    }

    /**
     * 處理郵件發送錯誤
     */
    private void handleEmailError(EmailMessageDTO emailMessage, Message message, Channel channel, Exception e) throws IOException {
        emailMessage.incrementRetryCount();
        
        if (emailMessage.isMaxRetryExceeded()) {
            log.error("郵件發送重試次數超限，丟棄消息: {}", emailMessage.getEmail());
            // 超過重試次數，拒絕消息且不重新入隊
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } else {
            log.warn("郵件發送失敗，將重試. 當前重試次數: {}/{}", 
                    emailMessage.getRetryCount(), emailMessage.getMaxRetryCount());
            // 拒絕消息並重新入隊
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
        }
    }

    /**
     * 生成6位數字驗證碼
     */
    private String generateVerificationCode() {
        StringBuilder code = new StringBuilder(CODE_LENGTH);
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }
        return code.toString();
    }

    /**
     * 發送郵件
     */
    private void sendEmail(String to, String verificationCode, EmailService.Type type) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(to);

        String subject;
        String text;

        switch (type) {
            case REGISTRATION:
                subject = "註冊驗證碼";
                text = String.format("您的註冊驗證碼是：%s\n\n驗證碼5分鐘內有效，請及時使用。", verificationCode);
                break;
            case LOGIN:
                subject = "登入驗證碼";
                text = String.format("您的登入驗證碼是：%s\n\n驗證碼5分鐘內有效，請及時使用。", verificationCode);
                break;
            case PASSWORD_RESET:
                subject = "密碼重置驗證碼";
                text = String.format("您的密碼重置驗證碼是：%s\n\n驗證碼5分鐘內有效，請及時使用。", verificationCode);
                break;
            default:
                subject = "驗證碼";
                text = String.format("您的驗證碼是：%s\n\n驗證碼5分鐘內有效，請及時使用。", verificationCode);
        }

        message.setSubject(subject);
        message.setText(text);

        // 發送郵件
        mailSender.send(message);
        
        log.info("郵件發送成功: email={}, type={}", to, type);
    }
}