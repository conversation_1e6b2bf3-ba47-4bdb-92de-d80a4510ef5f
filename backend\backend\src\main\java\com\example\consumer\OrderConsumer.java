package com.example.consumer;

import com.example.config.RabbitMQConfig;
import com.example.dto.OrderMessageDTO;
import com.example.service.OrderService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;

/**
 * 訂單消費者
 * 處理延時訂單取消等任務
 */
@Component
@Slf4j
public class OrderConsumer {

    @Autowired
    private OrderService orderService;

    /**
     * 處理延時訂單（通過死信隊列實現）
     * 當訂單在延時隊列中停留30分鐘後，會被轉發到這個隊列進行處理
     */
    @RabbitListener(queues = RabbitMQConfig.ORDER_PROCESS_QUEUE)
    public void handleDelayedOrder(OrderMessageDTO orderMessage, Message message, Channel channel) throws IOException {
        try {
            log.info("開始處理延時訂單: orderNumber={}, operationType={}", 
                    orderMessage.getOrderNumber(), orderMessage.getOperationType());

            switch (orderMessage.getOperationType()) {
                case CANCEL_ORDER:
                    handleOrderCancellation(orderMessage);
                    break;
                case PAYMENT_TIMEOUT:
                    handlePaymentTimeout(orderMessage);
                    break;
                case INVENTORY_RESTORE:
                    handleInventoryRestore(orderMessage);
                    break;
                default:
                    log.warn("未知的訂單操作類型: {}", orderMessage.getOperationType());
            }

            // 手動確認消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.info("延時訂單處理成功: orderNumber={}", orderMessage.getOrderNumber());

        } catch (Exception e) {
            log.error("處理延時訂單失敗: orderNumber={}", orderMessage.getOrderNumber(), e);
            
            // 訂單處理失敗，拒絕消息但不重新入隊（避免無限循環）
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    /**
     * 處理訂單取消
     */
    private void handleOrderCancellation(OrderMessageDTO orderMessage) {
        try {
            String orderNumber = orderMessage.getOrderNumber();
            
            // 檢查訂單是否已支付
            if (orderService.isOrderPaid(orderNumber)) {
                log.info("訂單已支付，無需取消: orderNumber={}", orderNumber);
                return;
            }
            
            // 取消訂單
            boolean cancelled = orderService.cancelOrder(orderNumber);
            if (cancelled) {
                log.info("訂單取消成功: orderNumber={}", orderNumber);
                
                // 恢復庫存
                orderService.restoreInventory(orderNumber);
                log.info("庫存恢復成功: orderNumber={}", orderNumber);
                
                // 發送取消通知郵件（如果有用戶郵箱）
                if (orderMessage.getUserEmail() != null) {
                    // 這裡可以發送訂單取消通知郵件
                    log.info("應發送訂單取消通知郵件: email={}, orderNumber={}", 
                            orderMessage.getUserEmail(), orderNumber);
                }
            } else {
                log.warn("訂單取消失敗: orderNumber={}", orderNumber);
            }
            
        } catch (Exception e) {
            log.error("處理訂單取消失敗: orderNumber={}", orderMessage.getOrderNumber(), e);
            throw e;
        }
    }

    /**
     * 處理支付超時
     */
    private void handlePaymentTimeout(OrderMessageDTO orderMessage) {
        try {
            String orderNumber = orderMessage.getOrderNumber();
            
            // 檢查訂單支付狀態
            if (orderService.isOrderPaid(orderNumber)) {
                log.info("訂單已支付，無需處理超時: orderNumber={}", orderNumber);
                return;
            }
            
            // 標記訂單為支付超時
            orderService.markOrderAsPaymentTimeout(orderNumber);
            log.info("訂單已標記為支付超時: orderNumber={}", orderNumber);
            
            // 恢復庫存
            orderService.restoreInventory(orderNumber);
            log.info("庫存恢復成功（支付超時）: orderNumber={}", orderNumber);
            
        } catch (Exception e) {
            log.error("處理支付超時失敗: orderNumber={}", orderMessage.getOrderNumber(), e);
            throw e;
        }
    }

    /**
     * 處理庫存恢復
     */
    private void handleInventoryRestore(OrderMessageDTO orderMessage) {
        try {
            String orderNumber = orderMessage.getOrderNumber();
            
            // 恢復庫存
            orderService.restoreInventory(orderNumber);
            log.info("庫存恢復成功: orderNumber={}", orderNumber);
            
        } catch (Exception e) {
            log.error("處理庫存恢復失敗: orderNumber={}", orderMessage.getOrderNumber(), e);
            throw e;
        }
    }

    /**
     * 處理死信隊列中的消息
     * 用於處理無法正常處理的消息
     */
    @RabbitListener(queues = RabbitMQConfig.DEAD_LETTER_QUEUE)
    public void handleDeadLetter(OrderMessageDTO orderMessage, Message message, Channel channel) throws IOException {
        try {
            log.warn("收到死信消息: orderNumber={}, operationType={}, createTime={}", 
                    orderMessage.getOrderNumber(), 
                    orderMessage.getOperationType(),
                    orderMessage.getCreateTime());

            // 記錄死信消息到數據庫或日誌系統
            recordDeadLetterMessage(orderMessage);

            // 手動確認消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            
        } catch (Exception e) {
            log.error("處理死信消息失敗: orderNumber={}", orderMessage.getOrderNumber(), e);
            
            // 即使死信處理失敗，也要確認消息，避免無限循環
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }
    }

    /**
     * 記錄死信消息
     */
    private void recordDeadLetterMessage(OrderMessageDTO orderMessage) {
        // 這裡可以將死信消息記錄到數據庫或發送到監控系統
        log.error("死信消息記錄: orderNumber={}, operationType={}, createTime={}, recordTime={}", 
                orderMessage.getOrderNumber(), 
                orderMessage.getOperationType(),
                orderMessage.getCreateTime(),
                LocalDateTime.now());
        
        // 可以考慮發送告警通知給運維人員
    }
}