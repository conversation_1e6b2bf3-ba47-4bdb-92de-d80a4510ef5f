package com.example.service;

import com.example.config.RabbitMQConfig;
import com.example.dto.EmailMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 郵件生產者服務
 * 將郵件發送任務異步化，提高系統響應速度
 */
@Service
@Slf4j
public class EmailProducerService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 發送註冊驗證碼郵件到隊列
     */
    public void sendRegistrationEmail(String email) {
        EmailMessageDTO message = new EmailMessageDTO(email, EmailService.Type.REGISTRATION);
        rabbitTemplate.convertAndSend(RabbitMQConfig.EMAIL_REGISTRATION_QUEUE, message);
        log.info("註冊郵件消息已發送到隊列: {}", email);
    }

    /**
     * 發送註冊驗證碼郵件到隊列（帶用戶ID）
     */
    public void sendRegistrationEmail(String email, Long userId) {
        EmailMessageDTO message = new EmailMessageDTO(email, EmailService.Type.REGISTRATION, userId);
        rabbitTemplate.convertAndSend(RabbitMQConfig.EMAIL_REGISTRATION_QUEUE, message);
        log.info("註冊郵件消息已發送到隊列: email={}, userId={}", email, userId);
    }

    /**
     * 發送登入驗證碼郵件到隊列
     */
    public void sendLoginEmail(String email) {
        EmailMessageDTO message = new EmailMessageDTO(email, EmailService.Type.LOGIN);
        rabbitTemplate.convertAndSend(RabbitMQConfig.EMAIL_LOGIN_QUEUE, message);
        log.info("登入郵件消息已發送到隊列: {}", email);
    }

    /**
     * 發送登入驗證碼郵件到隊列（帶用戶ID）
     */
    public void sendLoginEmail(String email, Long userId) {
        EmailMessageDTO message = new EmailMessageDTO(email, EmailService.Type.LOGIN, userId);
        rabbitTemplate.convertAndSend(RabbitMQConfig.EMAIL_LOGIN_QUEUE, message);
        log.info("登入郵件消息已發送到隊列: email={}, userId={}", email, userId);
    }

    /**
     * 發送密碼重置驗證碼郵件到隊列
     */
    public void sendPasswordResetEmail(String email) {
        EmailMessageDTO message = new EmailMessageDTO(email, EmailService.Type.PASSWORD_RESET);
        rabbitTemplate.convertAndSend(RabbitMQConfig.EMAIL_PASSWORD_RESET_QUEUE, message);
        log.info("密碼重置郵件消息已發送到隊列: {}", email);
    }

    /**
     * 發送密碼重置驗證碼郵件到隊列（帶用戶ID）
     */
    public void sendPasswordResetEmail(String email, Long userId) {
        EmailMessageDTO message = new EmailMessageDTO(email, EmailService.Type.PASSWORD_RESET, userId);
        rabbitTemplate.convertAndSend(RabbitMQConfig.EMAIL_PASSWORD_RESET_QUEUE, message);
        log.info("密碼重置郵件消息已發送到隊列: email={}, userId={}", email, userId);
    }

    /**
     * 發送自定義郵件消息到指定隊列
     */
    public void sendCustomEmailMessage(String queueName, EmailMessageDTO emailMessage) {
        rabbitTemplate.convertAndSend(queueName, emailMessage);
        log.info("自定義郵件消息已發送到隊列: queue={}, email={}", queueName, emailMessage.getEmail());
    }

    /**
     * 發送郵件消息（不檢查頻率限制）
     * 適用於系統內部通知等場景
     */
    public void sendEmailWithoutRateLimit(String email, EmailService.Type type) {
        EmailMessageDTO message = new EmailMessageDTO(email, type);
        message.setCheckRateLimit(false);
        
        String queueName;
        switch (type) {
            case REGISTRATION:
                queueName = RabbitMQConfig.EMAIL_REGISTRATION_QUEUE;
                break;
            case LOGIN:
                queueName = RabbitMQConfig.EMAIL_LOGIN_QUEUE;
                break;
            case PASSWORD_RESET:
                queueName = RabbitMQConfig.EMAIL_PASSWORD_RESET_QUEUE;
                break;
            default:
                queueName = RabbitMQConfig.EMAIL_REGISTRATION_QUEUE;
        }
        
        rabbitTemplate.convertAndSend(queueName, message);
        log.info("郵件消息已發送到隊列（無頻率限制）: email={}, type={}", email, type);
    }
}