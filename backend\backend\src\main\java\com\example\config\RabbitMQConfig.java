package com.example.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class RabbitMQConfig {

    // 隊列名稱常量
    public static final String EMAIL_QUEUE = "email_queue";
    public static final String EMAIL_REGISTRATION_QUEUE = "email_registration_queue";
    public static final String EMAIL_LOGIN_QUEUE = "email_login_queue";
    public static final String EMAIL_PASSWORD_RESET_QUEUE = "email_password_reset_queue";
    
    // 延時隊列相關
    public static final String ORDER_DELAY_QUEUE = "order_delay_queue";
    public static final String ORDER_PROCESS_QUEUE = "order_process_queue";
    public static final String ORDER_EXCHANGE = "order_exchange";
    public static final String ORDER_DELAY_ROUTING_KEY = "order.delay";
    public static final String ORDER_PROCESS_ROUTING_KEY = "order.process";
    
    // 死信隊列相關
    public static final String DEAD_LETTER_EXCHANGE = "dead_letter_exchange";
    public static final String DEAD_LETTER_QUEUE = "dead_letter_queue";
    public static final String DEAD_LETTER_ROUTING_KEY = "dead.letter";

    /**
     * 配置RabbitTemplate使用JSON消息轉換器
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(new Jackson2JsonMessageConverter());
        return template;
    }

    /**
     * JSON消息轉換器
     */
    @Bean
    public Jackson2JsonMessageConverter jackson2JsonMessageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    // =============== 郵件隊列配置 ===============

    /**
     * 註冊郵件隊列
     */
    @Bean
    public Queue emailRegistrationQueue() {
        return QueueBuilder.durable(EMAIL_REGISTRATION_QUEUE).build();
    }

    /**
     * 登入郵件隊列
     */
    @Bean
    public Queue emailLoginQueue() {
        return QueueBuilder.durable(EMAIL_LOGIN_QUEUE).build();
    }

    /**
     * 密碼重置郵件隊列
     */
    @Bean
    public Queue emailPasswordResetQueue() {
        return QueueBuilder.durable(EMAIL_PASSWORD_RESET_QUEUE).build();
    }

    // =============== 延時隊列配置 ===============

    /**
     * 訂單交換機
     */
    @Bean
    public DirectExchange orderExchange() {
        return new DirectExchange(ORDER_EXCHANGE, true, false);
    }

    /**
     * 死信交換機
     */
    @Bean
    public DirectExchange deadLetterExchange() {
        return new DirectExchange(DEAD_LETTER_EXCHANGE, true, false);
    }

    /**
     * 訂單延時隊列（用於延時取消訂單）
     * 30分鐘後消息過期，轉發到死信隊列
     */
    @Bean
    public Queue orderDelayQueue() {
        Map<String, Object> args = new HashMap<>();
        // 設置消息過期時間：30分鐘
        args.put("x-message-ttl", 30 * 60 * 1000);
        // 設置死信交換機
        args.put("x-dead-letter-exchange", DEAD_LETTER_EXCHANGE);
        // 設置死信路由鍵
        args.put("x-dead-letter-routing-key", ORDER_PROCESS_ROUTING_KEY);
        
        return QueueBuilder.durable(ORDER_DELAY_QUEUE)
                .withArguments(args)
                .build();
    }

    /**
     * 訂單處理隊列（死信隊列，處理過期訂單）
     */
    @Bean
    public Queue orderProcessQueue() {
        return QueueBuilder.durable(ORDER_PROCESS_QUEUE).build();
    }

    /**
     * 死信隊列
     */
    @Bean
    public Queue deadLetterQueue() {
        return QueueBuilder.durable(DEAD_LETTER_QUEUE).build();
    }

    // =============== 隊列綁定配置 ===============

    /**
     * 綁定延時隊列到訂單交換機
     */
    @Bean
    public Binding orderDelayBinding() {
        return BindingBuilder.bind(orderDelayQueue())
                .to(orderExchange())
                .with(ORDER_DELAY_ROUTING_KEY);
    }

    /**
     * 綁定處理隊列到死信交換機
     */
    @Bean
    public Binding orderProcessBinding() {
        return BindingBuilder.bind(orderProcessQueue())
                .to(deadLetterExchange())
                .with(ORDER_PROCESS_ROUTING_KEY);
    }

    /**
     * 綁定死信隊列到死信交換機
     */
    @Bean
    public Binding deadLetterBinding() {
        return BindingBuilder.bind(deadLetterQueue())
                .to(deadLetterExchange())
                .with(DEAD_LETTER_ROUTING_KEY);
    }
}