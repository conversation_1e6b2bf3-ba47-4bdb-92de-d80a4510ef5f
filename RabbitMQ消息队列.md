

# RabbitMQ消息队列

## 一、消息队列概述

消息队列（Message Queue），即存放消息的一种队列。由于是队列所以具备先进先出（FIFO）的特点。MQ是一种第三方的中间件，与平台语言无关。消息**生产者**将消息存入到队列中，消息**消费者**从队列中取出消息并使用。MQ广泛的应用于进程间通讯，高并发异步处理，请求削峰等场景中。

MQ的典型应用场景：

- **异步处理**

比如在注册时需要发送邮件或短信，如果以同步的方式去执行就需要等待邮件和短信发送结束才能对本次请求做出响应。

![img](https://cdn.nlark.com/yuque/0/2023/png/35234027/1696733576691-6714b8bb-ae55-4fd4-a7e3-d1f17caebdaa.png)

为了提高减少响应时间，我们可以采用消息队列优化这个业务流程，在向用户发送验证码的业务中并没有真正的调用第三方接口发送短信，而是将用户的手机号码存入队列中，同时程序中有一个消费者线程会不断的从队列中取出手机号码来完成发送短信的任务。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/35234027/1696733576725-e90fb18d-a45d-45cd-b2f7-ceb6987b3346.png?x-oss-process=image%2Fformat%2Cwebp)

比如在电商系统中，用户付款成功之后需要执行2个操作：

1.修改商品库存

2.增加用户消费积分

传统的执行流程如下：

![image.png](https://cdn.nlark.com/yuque/0/2023/png/35234027/1696733576718-7ded1f11-b9bc-443c-b151-d29fcf4f6b72.png?x-oss-process=image%2Fformat%2Cwebp)

首先要明确支付操作用户最好只让用户执行一次，如果此时修改积分的操作失败怎么办？修改库存的操作回滚还是不回滚？如果选择不回滚，那么什么时候去修改积分？如果回滚了难道先去执行一次退款然后让用户重新执行支付流程，这显然是一种很差的用户体验。这就是程序之间的耦合性太强所带来的的不便，我们可以使用MQ对该流程进行优化。

![image-20240814150828404](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240814150828404.png)

由于MQ具备很高的可靠性，MQ中的消息在消费成功之前不会丢失，即使消费失败也会不断的去重试直到成功为止，这种方案的弊端是无法做到数据的强一致性，只能是保证最终一致性，是否需要采用这种方案，这就需要具体的去权衡业务。

- **流量削峰**

有大量的请求同时进入到服务器，如果直接对业务进行处理，由于业务中存在一些耗时操作导致单个请求处理时间较长，服务器无法同时处理这么多请求。

![image-20240814150906146](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240814150906146.png)

此时可以直接将要处理的关键数据保存到队列中，然后直接响应请求。在通过消费者从队列中取出数据慢慢的处理。通过这种方案可以提高应用程序的处理能力。

![image-20240814150921546](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240814150921546.png)

- **延迟处理**

比如用户在下单之后，我们在生成订单信息的同时会将订单中购买的商品库存量冻结一部分。但是如果一直不进行支付，这部分冻结的商品无法被其他用户购买，所以我们需要在一段时间以后再次检查订单的支付状态，如果用户依然没有进行支付，那么我们将对订单做取消处理，并同时将冻结的库存量还原。

![image-20240814150950699](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240814150950699.png)

## 二、RabbitMQ环境搭建

RabbitMQ是MQ队列中的一种，是流行的开源消息队列系统，用erlang语言开发。RabbitMQ是AMQP（高级消息队列协议）的标准实现。支持多种客户端，如：Python、Ruby、.NET、Java、JMS、C、PHP、ActionScript、XMPP、STOMP等，支持AJAX，持久化。用于在分布式系统中存储转发消息，在易用性、扩展性、高可用性等方面表现不俗。除了RabbitMQ之外还有ActiveMQ、ZeroMQ、kafka，以及阿里开源的RocketMQ。

### 1、在Windows上安装RabbitMQ

##### （1）下载并安装erlang

具体教程：https://blog.csdn.net/yinying293/article/details/140969394

erlang官网：https://www.erlang.org/downloads

##### （2）配置erlang环境变量

变量名：ERLANG_HOME

变量值：erlang安装目录

![img](https://cdn.nlark.com/yuque/0/2023/png/35234027/1696734659408-4149fe93-1ea9-4001-83fd-83ed498de075.png)

在path中增加一段:%ERLANG_HOME%\bin

![img](https://cdn.nlark.com/yuque/0/2023/png/35234027/1696734696173-0bd63ff3-5445-439c-811a-63d7b963907c.png)

##### (3)检查安装

在cmd控制台通过命令查看erlang版本

```shell
erl -version
```

![img](https://cdn.nlark.com/yuque/0/2023/png/35234027/1696735615374-d1b56d3d-3423-4e20-8fb6-18fc2f69dc10.png)

##### （3）下载安装rabbitmq

rabbitmq官网：https://www.rabbitmq.com/install-windows.html#installer

##### （4）重新安装服务

rabbitmq在安装成功以后，会自动为计算机安装一个rabbitmq服务，但是默认情况下rabbitmq的数据存放位置是在当前用户目录下的，如果你的用户目录为中文，那么该服务是无法正常启动的。所以需要重新安装服务

- 用管理员打开cmd

![img](https://cdn.nlark.com/yuque/0/2023/png/35234027/1696733683893-4450018e-3b73-4886-849c-9a380615326e.png)

- 将目录跳转到安装目录的sbin目录中

![img](https://cdn.nlark.com/yuque/0/2023/png/35234027/1696736303448-b24f9484-5464-422a-8b60-66ce716f1d37.png)

- 执行命令删除默认安装的服务

```shell
rabbitmq-service.bat remove
```

- 设置rabbitmq中数据存储目录

```shell
set RABBITMQ_BASE=E:\rabbitmq\rabbitmq-data
```

- 安装rabbitmq服务

```shell
rabbitmq-service.bat install
```

- 激活rabbitmq的管理界面

```shell
rabbitmq-plugins enable rabbitmq_management
```

- 启动服务

```shell
net start rabbitmq
```

- 访问管理界面

![img](https://cdn.nlark.com/yuque/0/2023/png/35234027/1696736466590-90455a79-5574-489d-9979-c297145ee0c0.png)

其中登录的用户名和密码分别为：guest/guest

### 三、RabbitMQ基本工作原理

#### 3.1、工作过程详解

所有中间件技术都是基于 TCP/IP 协议基础之上进行构建新的协议规范，RabbitMQ遵循的是**AMQP**协议（Advanced Message Queuing Protocol - 高级消息队列协议）。

<img src="https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240814152603653.png" alt="image-20240814152603653" style="zoom:50%;" />

生产者发送消息的流程：【生产者的目的就是将消息放到队列中】

- 1、生产者和`Broker`建立`TCP`连接；
- 2、生产者和`Broker`建立通道；
- 3、生产者通过通道消息发送给`Broker`，由`Exchange`将消息进行转发；
- 4、`Exchange`将消息转发到指定的`Queue`（队列）。

```txt
具体细节：
1、消息生产者连接到`RabbitMQ Broker`,建立链接（Connection）,在链接（Connection）上开启一个信道（Channel）；
2、声明一个交换机（Exchange）,并设置相关属性，比如交换机类型、是否持久化等；
3、声明一个队列（Queue），并设置相关属性，比如是否排他、是否持久化、是否自动删除等；
4、使用路由键（RoutingKey）将队列（Queue）和交换机（Exchange）绑定起来；
5、生产者发送消息至 RabbitMQ Broker，其中包含路由键、交换机等信息，根据路由键（RoutingKey）发送消息到交换机（Exchange）；
6、相应的交换器（Exchange）根据接收到的路由键（RoutingKey）查找相匹配的队列如果找到 ，则将从生产者发送过来的消息存入相应的队列中；
7、如果没有找到 ，则根据生产者配置的属性选择丢弃还是回退给生产者；
8、关闭信道（Channel）；
9、关闭链接（Connection）；
```

消费者接收消息流程：【消费者的目的是将队列中的消息取出来进行消费】

- 1、消费者和`Broker`建立`TCP`连接；
- 2、消费者和`Broker`建立通道；
- 3、消费者监听指定的`Queue`（队列）；
- 4、当有消息到达`Queue`时`Broker`默认将消息推送给消费者；
- 5、消费者接收到消息；
- **6、`ack`回复。**

```txt
具体细节：
1、建立链接（Connection）；
2、在链接（Connection）上开启一个信道（Channel）；
3、请求消费指定队列（Queue）的消息，并设置回调函数（onMessage）；
4、[MQ]将消息推送给消费者，消费者接收消息；
5、消费者发送消息确定（Ack[acknowledge]）；
6、[MQ]删除被确认的消息；
7、关闭信道（Channel）；
8、关闭链接（Connection）
```

#### 3.2、名称解释

- **Producer：** 消息生产者，即生产方客户端，生产方客户端将消息发送；
- **Connection：**TCP连接，生产者或消费者与消息队列RabbitMQ版间的物理TCP连接；
- **Channel：**在客户端的每个物理TCP连接里，可建立多个Channel，每个Channel代表一个会话任务。

```txt
1）Channel是物理TCP连接中的虚拟连接。
2）当应用通过Connection与消息队列RabbitMQ版建立连接后，所有的AMQP协议操作（例如创建队列、发送消息、接收消息等）都会通过Connection中的Channel完成。
3） Channel可以复用Connection，即一个Connection下可以建立多个Channel。
4） Channel不能脱离Connection独立存在，而必须存活在Connection中。
5） 当某个Connection断开时，该Connection下的所有Channel都会断开。
```

- **Broker：**消息队列服务进程，此进程包括两个部分：Exchange和Queue；
- **Exchange（交换器）：**生产者将消息发送到Exchange，由Exchange将消息路由到一个或多个Queue中。Exchange根据消息的属性或内容路由消息。
- **Queue：**消息队列，存储消息的队列，每个消息都会被投入到一个或多个Queue里；
- **Consumer：**消息消费者，即消费方客户端，接收MQ转发的消息；
- **Routing Key(路由键)：**生产者在向Exchange发送消息时，需要指定一个Routing Key来设定该消息的路由规则。 Routing Key需要与Exchange类型及Binding Key联合使用才能生效。一般情况下，生产者在向Exchange发送消息时，可以通过指定Routing Key来决定消息被路由到哪个或哪些Queue；
- **Binding：**一套绑定规则，用于告诉Exchange消息应该被存储到哪个Queue。它的作用是把Exchange和Queue按照路由规则绑定起来。
- **Binding Key(绑定键)：**用于告知Exchange应该将消息投递到哪些Queue中（生产者将消息发送给哪个Exchange是需要由RoutingKey决定的，生产者需要将Exchange与哪个队列绑定时需要由BindingKey决定的）；
- **Virtual Host：**虚拟主机，本质上是一个mini版的RabbitMQ服务器，拥有自己的队列、交换机、绑定和权限机制，vhost是共享相同的身份认证和加密环境的独立服务器域。vhost是AMQP的基础，必须在连接时指定，RabbitMQ默认的vhost是。

#### 3.3、消息推送模式

1、一种是Pull模式，对应的方法是basicGet。 消息存放在服务端，只有消费者主动获取才能拿到消息。如果每搁一段时间获取一次消息，消息的实时性会降低。 但是好处是可以根据自己的消费能力决定消息的频率。 



2、另一种是push,对应的方法是BasicConsume，只要生产者发消息到服务器，就马上推送给消费者， 消息保存客户端，实时性很高，如果消费不过来有可能会造成消息积压。**Spring AMQP是push方式， 通过事件机制对队列进行监听，只要有消息到达队列，就会触发消费消息的方法。**

### 四、SpringBoot整合RabbitMQ

#### 4.1、创建用于及授权

由于一个RabbitMQ服务可能会支撑多个系统，为了保障这多个系统间数据互不干扰RabbitMQ中引入了虚拟主机的概念【优点类似数据库系统的不同数据库】。往往一个系统对应一个虚拟主机。因此我们要在系统中使用rabbitMQ，首要任务是创建对应的虚拟主机。

**创建虚拟主机：**

![image-20240814155231323](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240814155231323.png)

**创建用户：**

![image-20240814154923251](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240814154923251.png)

**用户授权：**

默认新创建的用户是不能访问任何一个虚拟主机的，我们需要对创建的用户进行授权操作

![image-20240814155128356](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240814155128356.png)

**用户授权**

默认新创建的用户是不能访问任何一个虚拟主机的，我们需要对创建的用户进行授权操作

![image-20240814155538215](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240814155538215.png)

#### 4.2、整合

引入依赖

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-amqp</artifactId>
</dependency>
```

配置

```yml
# rabbitmq配置
rabbitmq:
    host: 127.0.0.1
    port: 5672
    username: root
    password: 123456
    virtual-host: zuxiashop
```

### 五、RabbitMQ的五种工作模式

 RabbitMQ 其实一共有六种工作模式：简单模式（Simple）、工作队列模式（Work Queue）、发布订阅模式（Publish/Subscribe）、路由模式（Routing）、通配符模式（Topic）、远程调用模式（RPC)。其中**发布订阅模式**、**路由模式**、**通配符模式**这三种模型都属于订阅模式，只不过它们之间进行路由的方式不同罢了。远程调用模式是 RPC 不属于MQ，所以最终统计下来就是五种工作模式。  

#### 5.1、直连简单模式

![image-*****************](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-*****************.png)

 结合示意图案例分析： RabbitMQ是一个消息代理，它接受和转发消息。我们可以把它抽象成一个货运仓库，当商家把商品打包放进仓库后，可以确定快递员最后一定会把快递送到收件人手里。  

>  示意图解释：
>
>  P（Producer / Publisher）:生产者， 一个发送消息的用户应用程序。
>
>  C（Consumer）: 消费者，一个用来等待接收消息的用户应用程序。
>
>  Queue（红色区域）：消息队列，作用是接收消息、缓存消息，队列只受主机的内存和磁盘限制。生产者将消息发送到队列，队列是存储消息的缓冲区，消费者从队列中获取消息。 
>
> **应用场景：** 简单的发送与接收，没有特别的处理。  

简单（直连）模式特点： 1、一个生产者对应一个消费者，通过队列进行消息传递。 2、使用默认的direct交换机。

**应用场景： 一个生产者生产消息、一个消费者消费消息**

##### （1）代码实现

- **通过rabbitMQ的配置类创建消息队列实列**

```java
@Configuration //程序一旦启动 则会自动创建一个队列 名为simpleQueue
public class RabbitMQConfig {
    @Bean
    public Queue simpleQueue(){
        /**
         * 参数说明
         * 1.队列名称--一般一个业务对应一个队列
         * 2.队列是否持久化--为了防止消息丢失 一般设置为true
         * 3.是否被当前连接使用 如果设置为true 只能被当前连接使用 不灵活 一般false
         * 4.如果没有使用 是否自动删除 为了防止数据丢失 一般false
         * 5.其他键值对配置信息
         */
        return new Queue("simpleQueue",true,false,false,null);
    }
}
```

- **添加消费者类**

重要注解：

@RabbitListener：消息监听器，会监听某个队列，当从该队列中取出消息时交给监听器中的某个方法来执行

@RabbitHandler：消息处理者，具体处理消息的某个方法

```java
@Component
@RabbitListener(queues = "simpleQueue")
//监听某个队列，一旦项目启动 则服务后台后默认开启一个线程 监控队列
public class SimpleConsumer {
    //一旦队列中有消息 则会自动触发执行被RabbitHandler修饰的方法
    @RabbitHandler
    public void handleMsg(String message){
        System.out.println(message);
    }
    
    @RabbitHandler
    public void handleMsg(Map<String,Object> mapMessage){
        System.out.println(mapMessage.get("phone"));
    }
}
```

- **编写controller测试消息发送**

通过RabbitMQTemplate实现消息的发送

```java
@RestController
@RequestMapping("/mq")
@Api(tags = "测试消息队列使用")
public class RabbitMQController {
    //注入消息队列操作模板
    @Resource
    private RabbitTemplate rabbitTemplate;

    @PostMapping("/test")
    public Result testMQ(String param){
        rabbitTemplate.convertAndSend("simpleQueue",param);
        return Result.success().setMsg("消息发送成功");
    }
}
```

分析结果：当调用一次该接口时，可以在消息队列控制台看到队列中会多一条消息。如果我们发送的消息为 1 2 3 的顺序，则一旦将消费者配置进行系统后消费者打印的消息也是1 2 3 这样的一个顺序【遵循FIFO的规律】

##### （2）短信场景优化

在发送短信接口中不真正进行短信发送，而是将手机号以消息形式放在队列中。将同步代码转为异步代码，提高响应速度，增强用户体验

```java
@Component
@RabbitListener(queues = "simpleQueue")
//监听某个队列，一旦项目启动 则服务后台后默认开启一个线程 监控队列
public class SimpleConsumer {
    @Resource
    private MSMUtil msmUtil;
    @Resource
    private RedisUtil redisUtil;
    
    //一旦队列中有消息 则会自动触发执行被RabbitHandler修饰的方法
    @RabbitHandler
    public void handleMsg(String message){
        //2.如果注册了则发送短信
        try {
            String code = msmUtil.send(message);
            //存储redis
            redisUtil.setString(RedisKeyUtil.userLoginPhoneKey(message),code,24*60*60L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
}
```

**RabbitMQlisener放在方法上**

消费者监听器@RabbitListener注解既可以添加到类上也可以添加到方法上，**如果添加到类上那么该类中的所有方法只能监听同一个队列。注解添加到方法上，可以让每一个方法去监听不同的队列。**

比如我们系统中不仅登录需要发送短信、注册时也需要发送短信。但是我们一个业务往往对应一个一个队列，因此此处我们需要创建2个对象队列。然后编写不同的方法监听不同的队列实现不同的业务处理。

**创建2个队列：**

```java
@Configuration
public class RabbitMQConfig {

    @Bean
    public Queue RegistorCodeQueue(){
        return new Queue("registor_phone_queue",true,false,false,null);
    }

    @Bean
    public Queue LoginCodeQueue(){
        return new Queue("login_phone_code",true,false,false,null);
    }
```

**对消费者方法绑定队列：**

```java
@Component
//监听某个队列，一旦项目启动 则服务后台后默认开启一个线程 监控队列
public class MsgSendConsumer {
    @Resource
    private MSMUtil msmUtil;
    @Resource
    private RedisUtil redisUtil;

    //一旦队列中有消息 则会自动触发执行被RabbitHandler修饰的方法
    @RabbitHandler
    @RabbitListener(queues = "login_phone_code")
    public void loginCodeSend(String message){
        //2.如果注册了则发送短信
        try {
            String code = msmUtil.send(message);
            //存储redis
            redisUtil.setString(RedisKeyUtil.userLoginPhoneKey(message),code,24*60*60L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @RabbitListener(queues = "registor_phone_queue")
    @RabbitHandler
    public void registorCodeSend(String phone){
        //没有注册则发送短信
        try {
            String code = msmUtil.send(phone);
            //保存redis
            redisUtil.setString(RedisKeyUtil.userRegisterPhoneKey(phone),code,5*60L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
```

#### 5.2、工作模式

![image-20240814163146630](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240814163146630.png)

> **说明：** 与简单模式相比，工作队列模式(Work Queue)多了一些消费者，该模式也使用direct交换机，应用于处理消息较多的情况。
>
> **特点： 一个队列对应多个消费者**，通过队列进行消息传递 一条消息只会被一个消费者消费 消息队列默认采用轮询的方式将消息平均发送给消费者 使用Rabbitmq默认交换机 direct
>
>  **应用场景：** 对于**任务过重或任务较多**情况使用工作队列可以**提高任务处理的速度**。 

**其实工作模式的本质就是在简单模式的基础上增加多个消费者，以提高消息消费的速度达到防止消息积压的目的**

##### （1）代码实现

- **创建工作模式队列**

```java
@Bean
public Queue myWorkQueue(){
    return new Queue("work_queue",true,false,false,null);
}
```

- **编写多个消费者同时监控同一个队列**

```java
@Component
@RabbitListener(queues = "work_queue")
public class WorkConcumer1 {

    @RabbitHandler
    public void recive(String msg){
        System.out.println("消费者1进行消费"+msg);
    }
}
--------------
@Component
@RabbitListener(queues = "work_queue")
public class WorkConcumer2 {

    @RabbitHandler
    public void recive(String msg){
        System.out.println("消费者2进行消费"+msg);
    }
}
```

- **编写测试接口：**

```java
@PostMapping("/work")
public Result testWorkQueue(String param){
    rabbitTemplate.convertAndSend("work_queue",param);
    return Result.success().setMsg("消息发送成功");
}
```

分析结果：当通过接口一条消息一条消息的发送，我们可以发现2个消费者会轮流消费【采用轮询机制】

![image.png](https://cdn.nlark.com/yuque/0/2023/png/35234027/1696755665836-7bbb7ac8-c48f-4536-b32a-481f4e056ca3.png?x-oss-process=image%2Fformat%2Cwebp)

- 抓取模式配置

由于消费者进行消息抓取时并不是一条一条抓取而是默认一次抓取多条数据。由于各个消费者的处理能力不同，为了保障系统的高效率我们应该对抓取消息的数量进行设置

![image-20240814163534559](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240814163534559.png)

理论上预抓取数量可以随意设置，采用这样的配置弊端是会增加网络交互次数，是否需要采用这样的方式取决于实际情况。比如说处理业务的耗时远远小于从MQ中获取消息的耗时，那就应该把这个值设置的更大一些，一般是设置10-30之间。

#### 5.3、发布订阅模式

![image-20240814163921180](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240814163921180.png)

发布订阅模式指生产者将消息分发给绑定到交换机上的多个队列，生产者通过交换机每发送一条信息，绑定到交换机上的每一个队列都会收到该消息。【也就是消息会被推动到多个队列上】

**绑定模式为：一个交换机绑定多个队列，一个队列绑定一个或者多个消费者**

##### （1）代码实现

- **创建交换机**

```java
@Bean
public FanoutExchange getFanoutExchange(){ //创建交换机
    return new FanoutExchange("myExchange");
}
```

```java
@Bean
public Queue getQueue1(){ //创建队列1
    return new Queue("sub_queue_num1",true,false,false,null);
}

@Bean
public Queue getQueue2(){ //创建队列2
    return new Queue("sub_queue_num2",true,false,false,null);
}
```

**绑定交换机**

```java
@Bean
public Binding bindingQueue1(){
    return BindingBuilder.bind(getQueue1()).to(getFanoutExchange());
}

@Bean
public Binding bindingQueue2(){
    return BindingBuilder.bind(getQueue2()).to(getFanoutExchange());
}
```

**编写消息发送**

```java
@PostMapping("/pub")
public Result testPublish(){
    Map<String,String> map =  new HashMap<>();
    map.put("phone","123");
    map.put("code","456");
    //发送消息往信道中发送，信道发送给与之绑定的交换机
    rabbitTemplate.convertAndSend("myExchange","",map);
    return Result.success().setMsg("消息发送成功");
}
```

**编写消费者**

```java
@Component
public class PublishConsumer {

    @RabbitHandler
    @RabbitListener(queues = "sub_queue_num1")
    public void recive01(Map<String,Object> map){
        System.out.println("处理队列1数据"+map);
    }
    
    @RabbitListener(queues = "sub_queue_num2")
    public void recive02(Map<String,Object> map){
        System.out.println("处理队列2数据"+map);
    }
}
```

#### 5.4、路由模式

![image-*****************](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-*****************.png)

> **说明：**路由（Routing）模式是发布订阅模式的升级版。我们知道发布订阅模式是**无条件地**将所有消息分发给所有消费者队列，每个队列中都有相同的消息；路由模式，由上图很容易理解，每个队列消息会因为绑定的路由不同而不同。
>
> **特点：**
>
> 1、每个队列绑定一个路由关键字RoutingKey，生产者将带有RoutingKey的消息发送给交换机，交换机再根据路由 RoutingKey关键字将消息定向发送到指定的队列中；
>
> 2、默认使用 direct 交换机。
>
>  **应用场景：**
>
> 1、如在电商网站的促销活动中，双十一搞促销活动会把促销消息发布到所有队列中去；而一些小的促销活动为了节约成本，只发布到站内信队列。
>
> 2、为了节省磁盘空间，需要将重要的错误消息引导到日志文件，同时仍然能够在控制台上打印输出所有日志消息。  

路由模式和发布订阅模式类似，生产者也是通过交换机将消息发送给队列。区别是，**可以通过一个特定的路由键将消息有目的的发送给某一个队列或某几个队列，而不是所有的队列都能收到这条消息**。

##### （1）代码实现

- **获取路由模式信道**

```java
   //路由模式
@Bean
public DirectExchange getDirectExchange(){
    return new DirectExchange("my_direct_exchange");
}
```

- **获取队列**

```java
//创建队列
@Bean
public Queue getDirQueue01(){
    return new Queue("dir_queue01",true,false,false,null);
}

@Bean
public Queue getDirQueue02(){
    return new Queue("dir_queue02",true,false,false,null);
}
```

- **将信道与队列绑定并指定队列的路由**

```java
//将信道与队列绑定
@Bean
public Binding bindingDireQueue01(){
    return BindingBuilder.bind(getDirQueue01()).to(getDirectExchange()).with("a");
}

@Bean
public Binding bindingDireQueue02(){
    return BindingBuilder.bind(getDirQueue02()).to(getDirectExchange()).with("b");
}
```

- **发送消息并指定路由**

```java
@PostMapping("/dir")
public Result testDirect(){
    Map<String,String> map =  new HashMap<>();
    map.put("phone","123");
    map.put("code","456");
   int num =  (int)(Math.random()*10);
    System.out.println(num);
   if(num%2==0){
       rabbitTemplate.convertAndSend("my_direct_exchange","a",map);
   }else{
       rabbitTemplate.convertAndSend("my_direct_exchange","b",map);
   }
   return Result.success().setMsg("消息发送成功");
}
```

路由模式下发送消息时需要指定路由键，该消息具体发送到那个队列中由路由键强匹配决定。

#### 5.5、主题模式【通配符模式】

![image-*****************](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-*****************.png)

**说明：**通配符模式(Topic)是在路由模式的基础上升级，给队列绑定带通配符的路由关键字，只要消息的RoutingKey 能实现通配符匹配而不再是固定的字符串，就会将消息转发到该队列。通配符模式比路由模式更灵活。 

**特点：**

1、消息设置RoutingKey时，RoutingKey由多个单词构成，中间以 . 分割。

2、队列设置RoutingKey时，**#可以匹配任意多个单词**，***可以匹配任意一个单词**。

3、使用 topic 交换机。 

**应用场景：通配符模式的匹配规则相对于路由模式要显得抽象，**比如工厂生产手机屏，虽然手机屏的品牌（vivo、华为、荣耀）有很多，但是只要有手机屏产出就会发送一条消息给厂商，时刻统计手机屏的数量。路由模式方案：每个手机品牌独占一个队列和交换机绑定一个唯一标识 RoutingKey 手机品牌；通配符模式：一个队列和交换机绑定一个通配的标识 RoutingKey 即可。  

##### （1）代码实现

- **创建交换机**

```java
@Bean
public TopicExchange topicExchange() {
    return new TopicExchange("topic");
}
```

- **创建队列**

```java
@Bean
public Queue taQueue() {
    return new Queue("topic-a",true);
}

@Bean
public Queue tbQueue() {
    return new Queue("topic-b",true);
}
```

- **交换机与队列绑定**

```java
@Bean
public Binding bindingTopicExchangeA(){
    //路由以.c结尾的
    return BindingBuilder.bind(taQueue()).to(topicExchange()).with("*.c");
}

@Bean
public Binding bindingTopicExchangeB(){
    return BindingBuilder.bind(tbQueue()).to(topicExchange()).with("b.*");
}
```

- **发送消息**

```java
@GetMapping("/mq/topic")
public void direct() throws Exception{
    HashMap<String,Object> map = new HashMap<String,Object>();
    map.put("id", "1");
    //传入交换机和路由键的名称
    rabbitTemplate.convertAndSend("direct","b.c",map);
}
```

### 六、使用注解方式实现

通过注解可以直接实现创建队列，不再需要通过配置类的形式创建队列

##### （1）简单模式

RabbitListener的queuestoDeclare

```java
@RabbitListener(queuesToDeclare = {
        @Queue(value = "login_phone_code") //通过queue注解创建队列并监听
})
```

##### （2）工作模式

工作模式操作与简单模式一致，只是多个消费者同时监听同一个队列

```java
@Component
@RabbitListener(queuesToDeclare = {
        @Queue(value = "work_queue")
})
public class WorkConcumer2 {

    @RabbitHandler
    public void recive(String msg){
        System.out.println("消费者2进行消费"+msg);
    }
}
```

##### （3）发布订阅模式

type:fanout

```java
@RabbitListener(bindings = {
        @QueueBinding(
                value = @Queue(value = "sub_queue_num2"),
                exchange = @Exchange(name = "my_work",type = "fanout")
        )
})
```

##### （4）路由模式

```java
@RabbitListener(bindings = {
        @QueueBinding(
                value = @Queue(value = "sub_queue_num2"),
                exchange = @Exchange(name = "my_work",type = "direct")
                key="xxx"
        )
})
```

##### （5）主题模式

```java
@RabbitListener(bindings = {
        @QueueBinding(
                value = @Queue(value = "sub_queue_num2"),
                exchange = @Exchange(name = "my_work",type = "topic")
                key="xxxx"
        )
})
```

### 七、死信队列

#### 7.1、概述

先从概念解释上搞清楚这个定义，死信，顾名思义就是无法被消费的消息，字面意思可以这样理 解，一般来说，producer 将消息投递到 broker 或者直接到 queue 里了，consumer 从 queue 取出消息进行消费，但某些时候由于特定的原因导致queue中的某些消息无法被消费，这样的消息如果没有后续的处理，就变成了死信，有死信自然就有了死信队列。

以下几种情况会导致消息变成死信：

- 消息被拒绝（Basic.Reject/Basic.Nack)，并且设置requeue参数为false;
- **消息过期；**
- 队列达到最大长度。

#### 7.2、死信队列模拟延迟队列

延迟队列用来存放延迟消息。延迟消息：指当消息被发送以后，不想让消费者立刻拿到消息，而是等待特定时间后，消费者才能拿到这个消息进行消费。

在AMQP协议中，或者RabbitMQ本身没有直接支持延迟队列的功能，但是有两种方案来间接实现：

- 方案1：采用rabbitmq-delayed-message-exchange 插件实现。（RabbitMQ 3.6.x开始支持）
- 方案2：通过前面所介绍的DLX和TTL模拟出延迟队列的功能。

#### 7.3、应用场景

延迟队列的使用场景有很多，比如：

用户下订单场景：用户下单后有30分钟的时间支付，若30分钟内没有支付，则将这个订单取消。

方案：用户下单后将取消订单的消息发送到延迟队列，延迟时间设置为30分钟。取消订单这个消息的订阅者程序在30分钟后收到消息，判断该订单的状态是否为已支付，若还没支付，则将该订单状态设置为：已取消。

定时遥控场景：用户想用手机远程遥控家里的智能设备在指定的时间工作。

方案：假设用户想要的操作是：开启热水器。首先，将开启热水器这个消息发送到延迟队列，延迟时间设置到用户想要的时间到现在时间的差值。开启热水器这个消息的订阅者程序在指定时间收到消息，再将指令推送到智能设备。

需要注意的是，延迟队列的消息是不能取消的，解决方案是：在消费消息的时候判断这个消息对应的业务的当前状态。例如：对于取消订单来说，收到消息时，读取这个消息所对应的数据库信息，如果已经是已付款状态了，就不进行任何操作了，如果是未支付状态，则改为已取消。

#### 7.4、代码实现

思路：将需要延时的消息发送到一个待过期时间的队列中，不给改队列绑定消费者。那么到达指定的时间后改队列中的消息会被转移到死信队列中。对该死信队列绑定对应的消费者，达到延时执行的目的。

**实现关键：开启失败重试机制**

 在开启重试模式后，重试次数耗尽，如果消息依然失败，则需要有MessageRecoverer接口来处理，它包含三种不同的实现:

1、RejectAndDontRequeueRecoverer:重试耗尽后，直接reject，丢弃消息。默认就是这种方式。

2、ImmediateRequeueMessageRecoverer:重试耗尽后，返回nack，消息重新入队(不建议采用：会出现死循	环)。

3、RepublishMessageRecoverer:重试耗尽后，将失败消息投递到指定的交换机。(推荐使用)

![image-20240814180725194](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240814180725194.png)





创建带过期时间队列并不绑定消费者，同时指定消费失败策略为投递到新的队列中

```java
@Configuration
public class DeadMsgQueueConfig {
    /**
     * 创建一个带过期时间的队列order_queue
     * @return
     */
    @Bean
    public Queue deadMsgQueue(){
        Map<String,Object> configMap = new HashMap<>();
        //设定配置
        //消息过期时间
        configMap.put("x-message-ttl",50*1000);
        //死信消息交换机
        configMap.put("x-dead-letter-exchange", "delay-ex");
        //死信消息路由键
        configMap.put("x-dead-letter-routing-key", "order");
        return new Queue("order_queue",true,false,false,configMap);
    }

    @Bean
    public MessageRecoverer messageRecoverer(RabbitTemplate rabbitTemplate) {
        // 需要配置交换机和绑定键
        return new RepublishMessageRecoverer(rabbitTemplate, "error-ex", "order1");
    }
}
```

死信队列消费者

```java
@Component
public class DeadMsgConsumer {

    @RabbitHandler
    @RabbitListener(bindings = {
            @QueueBinding(
                    value = @Queue(name = "delay_order_queue"),
                    exchange = @Exchange(name = "error-ex",type = "direct"),
                    key = "order"
            )
    })
    public void recive(Map<String,Object> msgMap){
        System.out.println("延时消息被处理");
        System.out.println(msgMap);
    }
}
```

消息投递到带过期时间的队列

应用：延时队列查询支付状态并更改数据库

```java
RLock lock = redissonClient.getLock("order_cancle" + orderNum);
try {
    if(lock.tryLock(20,100, TimeUnit.SECONDS)){
        try {
            //查询订单
            OrderInfo orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfo>() .eq(OrderInfo::getOrderNum, orderNum));
            String tradeState = "";
            //支付宝查询支付状态
            if(orderInfo!=null){
                AlipayTradeQueryRequest alipay_request = new AlipayTradeQueryRequest();
                AlipayTradeQueryModel model=new AlipayTradeQueryModel();
                model.setOutTradeNo(orderNum);
                alipay_request.setBizModel(model);
                AlipayTradeQueryResponse alipay_response =alipayClient.execute(alipay_request);
                tradeState = alipay_response.getBody();
                //订单存在且订单状态未支付
                if(!tradeState.contains("TRADE_SUCCESS")){
                    orderInfo.setOrderState(SysConstant.ORDER_STATE_CANCEL);
                    orderInfoMapper.updateById(orderInfo); //取消订单
                    //还原库存
                    List<OrderItem> orderItems = orderItemMapper.selectList(new LambdaQueryWrapper<OrderItem>()
                                                                            .eq(OrderItem::getOrderId, orderNum));
                    if(orderItems!=null&& !orderItems.isEmpty()){
                        for (OrderItem orderItem : orderItems) {
                            //回滚库存
                            redisUtil.incrString(RedisKeyUtil.bookCountKey(orderItem.getBookId()),orderItem.getBookCont().toString());
                        }
                    }
                }
            }
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        } finally {
            lock.unlock();
        }
    }
} catch (InterruptedException e) {
    throw new RuntimeException(e);
}
}
```

### 八、消费者消费失败补偿机制

#### 8.1、消费者自动重试机制

##### 1、概述

正常情况下一条消息被正常消费完毕后就会从队列中删除，然后消费者会进行下一条消息的消费。但是如果消费者中抛出异常，那么消息会一直在队列中无法出队，然后消费者会不断的进行重试消费【rabbitMQ的自动重试机制】。

##### 2、特点

优点：做为一种补偿机制，尽量让业务能正常执行完毕。【比如：消费者中需要获取某一把锁，但是这把锁一直没有被释放而导致等到超时获取锁失败】那如果没有重试机制，那么该业务就会得不到正常执行。自动重试机制的存在可以不断尝试，尽量让业务能正常执行。

缺点：如果消费者一直执行失败【这种情况基本很少，除非代码错误】那么就会不断重试，导致后面的消息一直得不到消费。

配置最大重试次数：

```yml
    listener:
      simple:
        retry:
          #开启重试机制
          enabled: true
          # 最大重试次数
          max-attempts: 5
          # 每次重试的最大间隔时间
          max-interval: 10000
          # 第一次重试的间隔时间
          initial-interval: 2000
          # 间隔时间乘子，当前间隔时间*乘子=下一次的间隔时间，最大不能超过设置的最大间隔时间
          multiplier: 2
```

如果超过最大重试次数还是消费失败，则消息会被投递到死信队列。

##### 3、代码实现

注意：由于我们在使用死信队列模拟延时队列的时候已经声明了一个死信队列，重试后消费没有成功的消息会被投递到死信队列中。而死信队列中绑定一个消费者方法进行处理。因此我们发消息时应该通过代码判定消息的来源

发送消息时：

![image-20240815170737774](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240815170737774.png)

处理消息时：

![image-20240815170832282](https://woniumd.oss-cn-hangzhou.aliyuncs.com/java/liyoupeng/image-20240815170832282.png)

