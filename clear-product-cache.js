/**
 * 清理商品緩存腳本
 * 用於清理Redis中特定商品的緩存數據
 */

const redis = require('redis');

// Redis配置
const REDIS_CONFIG = {
  host: 'localhost',
  port: 6379,
  // password: 'your-password', // 如果有密碼
};

// 要清理的商品ID
const PRODUCT_ID = 15;

async function clearProductCache() {
  let client;
  
  try {
    console.log('🔗 連接到Redis...');
    client = redis.createClient(REDIS_CONFIG);
    
    client.on('error', (err) => {
      console.error('Redis連接錯誤:', err);
    });
    
    await client.connect();
    console.log('✅ Redis連接成功');
    
    // 清理商品詳情緩存
    const detailKey = `product:detail:${PRODUCT_ID}`;
    console.log(`🗑️  清理商品詳情緩存: ${detailKey}`);
    await client.del(detailKey);
    
    // 清理商品列表相關緩存（使用通配符）
    const patterns = [
      'product:cache:*',           // 所有商品緩存
      'product:category:*',        // 分類緩存
      'product:list:*',           // 商品列表緩存
      'product:hot:*',            // 熱門商品緩存
      'product:search:*',         // 搜索結果緩存
    ];
    
    for (const pattern of patterns) {
      console.log(`🗑️  清理緩存模式: ${pattern}`);
      const keys = await client.keys(pattern);
      if (keys.length > 0) {
        await client.del(keys);
        console.log(`   清理了 ${keys.length} 個緩存鍵`);
      } else {
        console.log(`   沒有找到匹配的緩存鍵`);
      }
    }
    
    console.log('✅ 緩存清理完成');
    
  } catch (error) {
    console.error('❌ 緩存清理失敗:', error);
  } finally {
    if (client) {
      await client.quit();
      console.log('🔌 Redis連接已關閉');
    }
  }
}

// 執行清理
clearProductCache().then(() => {
  console.log('🎉 腳本執行完成');
  process.exit(0);
}).catch((error) => {
  console.error('💥 腳本執行失敗:', error);
  process.exit(1);
});
